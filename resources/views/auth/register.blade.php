<x-guest-layout>
    <x-laravel.authentication-card>
        <x-slot name="logo">
            <x-laravel.authentication-card-logo />
        </x-slot>

        <!-- <PERSON> Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                <x-icons.lucide.user class="w-8 h-8 text-indigo-600" />
            </div>
            <h1 class="text-3xl font-bold text-gray-900 mb-3">Join as Advertiser</h1>
            <p class="text-gray-600 text-lg">Start creating powerful campaigns to reach your audience</p>
        </div>

        <x-laravel.validation-errors class="mb-6" />

        <form method="POST" action="{{ route('register') }}" class="space-y-6">
            @csrf
            <x-honeypot />

            <!-- Personal Information Section -->
            <div class="space-y-6">
                <div class="border-l-4 border-indigo-500 pl-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-1">Personal Information</h3>
                    <p class="text-sm text-gray-600">Tell us about yourself</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Name Field -->
                    <div class="space-y-2">
                        <x-laravel.label for="name" value="{{ __('Full Name') }}" class="text-sm font-semibold text-gray-700" />
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <x-icons.lucide.user class="h-5 w-5 text-gray-400 group-focus-within:text-indigo-500 transition-colors" />
                            </div>
                            <x-laravel.input id="name"
                                class="block w-full pl-12 pr-4 py-3.5 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                                type="text"
                                name="name"
                                :value="old('name')"
                                required
                                autofocus
                                autocomplete="name"
                                placeholder="Enter your full name" />
                        </div>
                    </div>

                    <!-- Email Field -->
                    <div class="space-y-2">
                        <x-laravel.label for="email" value="{{ __('Email Address') }}" class="text-sm font-semibold text-gray-700" />
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <x-icons.lucide.mail class="h-5 w-5 text-gray-400 group-focus-within:text-indigo-500 transition-colors" />
                            </div>
                            <x-laravel.input id="email"
                                class="block w-full pl-12 pr-4 py-3.5 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                                type="email"
                                name="email"
                                :value="old('email')"
                                required
                                autocomplete="username"
                                placeholder="<EMAIL>" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div class="space-y-6">
                <div class="border-l-4 border-green-500 pl-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-1">Account Security</h3>
                    <p class="text-sm text-gray-600">Create a secure password for your account</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Password Field -->
                    <div class="space-y-2">
                        <x-laravel.label for="password" value="{{ __('Password') }}" class="text-sm font-semibold text-gray-700" />
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <x-icons.lucide.lock class="h-5 w-5 text-gray-400 group-focus-within:text-indigo-500 transition-colors" />
                            </div>
                            <x-laravel.input id="password"
                                class="block w-full pl-12 pr-4 py-3.5 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                                type="password"
                                name="password"
                                required
                                autocomplete="new-password"
                                placeholder="Create a strong password" />
                        </div>
                        <p class="text-xs text-gray-500">Minimum 8 characters with letters and numbers</p>
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="space-y-2">
                        <x-laravel.label for="password_confirmation" value="{{ __('Confirm Password') }}" class="text-sm font-semibold text-gray-700" />
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                <x-icons.lucide.check class="h-5 w-5 text-gray-400 group-focus-within:text-indigo-500 transition-colors" />
                            </div>
                            <x-laravel.input id="password_confirmation"
                                class="block w-full pl-12 pr-4 py-3.5 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200 bg-gray-50 focus:bg-white"
                                type="password"
                                name="password_confirmation"
                                required
                                autocomplete="new-password"
                                placeholder="Confirm your password" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Terms and Submit -->
            <div class="space-y-3">
                @if (Laravel\Jetstream\Jetstream::hasTermsAndPrivacyPolicyFeature())
                <div class="flex items-start">
                    <x-laravel.checkbox name="terms" id="terms" required class="mt-1 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" />
                    <div class="ml-2">
                        <p class="text-xs text-gray-600">
                            {!! __('I agree to the :terms_of_service and :privacy_policy', [
                            'terms_of_service' => '<a target="_blank" href="'.route('terms.show').'"
                                class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">'.__('Terms').'</a>',
                            'privacy_policy' => '<a target="_blank" href="'.route('policy.show').'"
                                class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">'.__('Privacy Policy').'</a>',
                            ]) !!}
                        </p>
                    </div>
                </div>
                @endif

                <x-laravel.button class="w-full justify-center py-2.5 text-sm font-medium">
                    {{ __('Create Advertiser Account') }}
                </x-laravel.button>
            </div>
        </form>

        <!-- Account Type Options -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            <div class="text-center mb-3">
                <p class="text-sm text-gray-600">Or choose a different account type</p>
            </div>

            <!-- Publisher Registration -->
            <div class="relative group">
                <a href="{{ route('publisher.register') }}"
                   class="block p-3 border-2 border-gray-200 bg-gray-50 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200 group-hover:shadow-md">
                    <div class="flex items-center justify-center mb-1">
                        <x-icons.lucide.layout-grid class="w-4 h-4 text-gray-600 group-hover:text-indigo-600 mr-2 transition-colors duration-200" />
                        <span class="text-sm font-semibold text-gray-700 group-hover:text-indigo-700 transition-colors duration-200">Register as Publisher</span>
                    </div>
                    <p class="text-xs text-gray-600 text-center">Monetize your website with our advertising network</p>
                </a>
            </div>
        </div>

        <!-- Sign In Link -->
        <div class="mt-4 text-center">
            <p class="text-xs text-gray-600">
                Already have an account?
                <a class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200 ml-1"
                   href="{{ route('login') }}">
                    {{ __('Sign in') }}
                </a>
            </p>
        </div>
    </x-laravel.authentication-card>
</x-guest-layout>