<x-guest-layout>
    <x-laravel.authentication-card>
        <x-slot name="logo">
            <x-laravel.authentication-card-logo />
        </x-slot>

        <!-- Page Header -->
        <div class="text-center mb-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Advertiser Registration</h1>
            <p class="text-gray-600">Create campaigns and reach your target audience</p>
        </div>

        <x-laravel.validation-errors class="mb-4" />

        <form method="POST" action="{{ route('register') }}" class="space-y-4">
            @csrf
            <x-honeypot />

            <!-- Name and Email Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Name Field -->
                <div class="space-y-1">
                    <x-laravel.label for="name" value="{{ __('Full Name') }}" class="text-sm font-medium text-gray-700" />
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <x-icons.lucide.user class="h-4 w-4 text-gray-400" />
                        </div>
                        <x-laravel.input id="name"
                            class="block w-full pl-9 pr-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                            type="text"
                            name="name"
                            :value="old('name')"
                            required
                            autofocus
                            autocomplete="name"
                            placeholder="Your full name" />
                    </div>
                </div>

                <!-- Email Field -->
                <div class="space-y-1">
                    <x-laravel.label for="email" value="{{ __('Email Address') }}" class="text-sm font-medium text-gray-700" />
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <x-icons.lucide.mail class="h-4 w-4 text-gray-400" />
                        </div>
                        <x-laravel.input id="email"
                            class="block w-full pl-9 pr-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                            type="email"
                            name="email"
                            :value="old('email')"
                            required
                            autocomplete="username"
                            placeholder="<EMAIL>" />
                    </div>
                </div>
            </div>

            <!-- Password Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <!-- Password Field -->
                <div class="space-y-1">
                    <x-laravel.label for="password" value="{{ __('Password') }}" class="text-sm font-medium text-gray-700" />
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <x-icons.lucide.lock class="h-4 w-4 text-gray-400" />
                        </div>
                        <x-laravel.input id="password"
                            class="block w-full pl-9 pr-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                            type="password"
                            name="password"
                            required
                            autocomplete="new-password"
                            placeholder="Strong password" />
                    </div>
                </div>

                <!-- Confirm Password Field -->
                <div class="space-y-1">
                    <x-laravel.label for="password_confirmation" value="{{ __('Confirm Password') }}" class="text-sm font-medium text-gray-700" />
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <x-icons.lucide.check class="h-4 w-4 text-gray-400" />
                        </div>
                        <x-laravel.input id="password_confirmation"
                            class="block w-full pl-9 pr-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                            type="password"
                            name="password_confirmation"
                            required
                            autocomplete="new-password"
                            placeholder="Confirm password" />
                    </div>
                </div>
            </div>

            <!-- Terms and Submit -->
            <div class="space-y-3">
                @if (Laravel\Jetstream\Jetstream::hasTermsAndPrivacyPolicyFeature())
                <div class="flex items-start">
                    <x-laravel.checkbox name="terms" id="terms" required class="mt-1 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" />
                    <div class="ml-2">
                        <p class="text-xs text-gray-600">
                            {!! __('I agree to the :terms_of_service and :privacy_policy', [
                            'terms_of_service' => '<a target="_blank" href="'.route('terms.show').'"
                                class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">'.__('Terms').'</a>',
                            'privacy_policy' => '<a target="_blank" href="'.route('policy.show').'"
                                class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">'.__('Privacy Policy').'</a>',
                            ]) !!}
                        </p>
                    </div>
                </div>
                @endif

                <x-laravel.button class="w-full justify-center py-2.5 text-sm font-medium">
                    {{ __('Create Advertiser Account') }}
                </x-laravel.button>
            </div>
        </form>

        <!-- Account Type Selection -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            <div class="text-center mb-3">
                <p class="text-xs text-gray-600">Choose your account type</p>
            </div>

            <div class="grid grid-cols-2 gap-3">
                <!-- Current: Advertiser Registration -->
                <div class="relative">
                    <div class="p-3 border-2 border-indigo-200 bg-indigo-50 rounded-lg">
                        <div class="flex items-center justify-center mb-1">
                            <x-icons.lucide.user class="w-4 h-4 text-indigo-600 mr-1" />
                            <span class="text-xs font-semibold text-indigo-900">Advertiser</span>
                        </div>
                        <p class="text-xs text-indigo-700 text-center mb-2">Create campaigns & reach audience</p>
                        <div class="text-center">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                Current Form
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Publisher Registration -->
                <div class="relative group">
                    <a href="{{ route('publisher.register') }}"
                       class="block p-3 border-2 border-gray-200 bg-gray-50 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200 group-hover:shadow-md">
                        <div class="flex items-center justify-center mb-1">
                            <x-icons.lucide.layout-grid class="w-4 h-4 text-gray-600 group-hover:text-indigo-600 mr-1 transition-colors duration-200" />
                            <span class="text-xs font-semibold text-gray-700 group-hover:text-indigo-700 transition-colors duration-200">Publisher</span>
                        </div>
                        <p class="text-xs text-gray-600 text-center mb-2">Monetize your website</p>
                        <div class="text-center">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700 group-hover:bg-indigo-100 group-hover:text-indigo-700 transition-colors duration-200">
                                Switch Form
                            </span>
                        </div>
                    </a>
                </div>
            </div>
        </div>

        <!-- Sign In Link -->
        <div class="mt-4 text-center">
            <p class="text-xs text-gray-600">
                Already have an account?
                <a class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200 ml-1"
                   href="{{ route('login') }}">
                    {{ __('Sign in') }}
                </a>
            </p>
        </div>
    </x-laravel.authentication-card>
</x-guest-layout>