<x-guest-layout>
    <x-laravel.authentication-card>
        <x-slot name="logo">
            <x-laravel.authentication-card-logo />
        </x-slot>

        <x-laravel.validation-errors class="mb-4" />

        <form method="POST" action="{{ route('register') }}">
            @csrf
            <x-honeypot />

            <div>
                <x-laravel.label for="name" value="{{ __('Name') }}" />
                <x-laravel.input id="name" class="block mt-1 w-full" type="text" name="name" :value="old('name')"
                    required autofocus autocomplete="name" />
            </div>

            <div class="mt-4">
                <x-laravel.label for="email" value="{{ __('Email') }}" />
                <x-laravel.input id="email" class="block mt-1 w-full" type="email" name="email" :value="old('email')"
                    required autocomplete="username" />
            </div>

            <div class="mt-4">
                <x-laravel.label for="password" value="{{ __('Password') }}" />
                <x-laravel.input id="password" class="block mt-1 w-full" type="password" name="password" required
                    autocomplete="new-password" />
            </div>

            <div class="mt-4">
                <x-laravel.label for="password_confirmation" value="{{ __('Confirm Password') }}" />
                <x-laravel.input id="password_confirmation" class="block mt-1 w-full" type="password"
                    name="password_confirmation" required autocomplete="new-password" />
            </div>

            @if (Laravel\Jetstream\Jetstream::hasTermsAndPrivacyPolicyFeature())
            <div class="mt-4">
                <x-laravel.label for="terms">
                    <div class="flex items-center">
                        <x-laravel.checkbox name="terms" id="terms" required />

                        <div class="ml-2">
                            {!! __('I agree to the :terms_of_service and :privacy_policy', [
                            'terms_of_service' => '<a target="_blank" href="'.route('terms.show').'"
                                class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">'.__('Terms
                                of Service').'</a>',
                            'privacy_policy' => '<a target="_blank" href="'.route('policy.show').'"
                                class="underline text-sm text-gray-600 hover:text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">'.__('Privacy
                                Policy').'</a>',
                            ]) !!}
                        </div>
                    </div>
                </x-laravel.label>
            </div>
            @endif

            <!-- Registration Options -->
            <div class="mt-6 pt-4 border-t border-gray-200">
                <div class="text-center mb-4">
                    <p class="text-sm text-gray-600">Choose your account type</p>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-6">
                    <!-- Advertiser Registration (Current Form) -->
                    <div class="relative">
                        <div class="p-4 border-2 border-indigo-200 bg-indigo-50 rounded-lg">
                            <div class="flex items-center justify-center mb-2">
                                <svg class="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                <span class="text-sm font-semibold text-indigo-900">Advertiser</span>
                            </div>
                            <p class="text-xs text-indigo-700 text-center mb-3">Create campaigns and reach your audience</p>
                            <x-laravel.button class="w-full text-xs py-2" type="submit">
                                {{ __('Register as Advertiser') }}
                            </x-laravel.button>
                        </div>
                    </div>

                    <!-- Publisher Registration -->
                    <div class="relative">
                        <div class="p-4 border-2 border-gray-200 bg-gray-50 rounded-lg hover:border-gray-300 hover:bg-gray-100 transition-all duration-200">
                            <div class="flex items-center justify-center mb-2">
                                <svg class="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z"></path>
                                </svg>
                                <span class="text-sm font-semibold text-gray-700">Publisher</span>
                            </div>
                            <p class="text-xs text-gray-600 text-center mb-3">Monetize your website with our ads</p>
                            <a href="{{ route('publisher.register') }}"
                               class="block w-full text-center px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                {{ __('Register as Publisher') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sign In Link -->
            <div class="flex items-center justify-center mt-6 pt-4 border-t border-gray-200">
                <p class="text-sm text-gray-600">
                    Already have an account?
                    <a class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-150 ml-1"
                       href="{{ route('login') }}">
                        {{ __('Sign in') }}
                    </a>
                </p>
            </div>
        </form>
    </x-laravel.authentication-card>
</x-guest-layout>