<x-guest-layout>
    @php
        $firstErrorField = $errors->keys()[0] ?? null;
    @endphp

    <x-laravel.authentication-card>
        <x-slot name="logo">
            <x-laravel.authentication-card-logo />
        </x-slot>

        <!-- Page Header -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Publisher Registration</h1>
            <p class="text-gray-600">Join our network and start monetizing your website</p>
        </div>

        <x-laravel.validation-errors class="mb-4" />

        <form method="POST" action="{{ route('publisher.register.store') }}" class="space-y-4">
            @csrf
            <x-honeypot />

            <!-- Name Field -->
            <div class="space-y-2">
                <x-laravel.label for="name" value="{{ __('Full Name') }}" class="text-sm font-medium text-gray-700" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <x-icons.lucide.user class="h-5 w-5 text-gray-400" />
                    </div>
                    <x-laravel.input id="name"
                        name="name"
                        type="text"
                        :value="old('name')"
                        required
                        autocomplete="name"
                        :autofocus="$firstErrorField === 'name'"
                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 {{ $errors->has('name') ? 'border-red-500 ring-red-500 ring-1' : '' }}"
                        placeholder="Enter your full name" />
                </div>
                @error('name')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Primary Domain Field -->
            <div class="space-y-2">
                <x-laravel.label for="primary_domain" value="Primary Domain" class="text-sm font-medium text-gray-700" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <x-icons.lucide.globe class="h-5 w-5 text-gray-400" />
                    </div>
                    <x-laravel.input id="primary_domain"
                        name="primary_domain"
                        type="text"
                        :value="old('primary_domain')"
                        required
                        placeholder="example.com"
                        :autofocus="$firstErrorField === 'primary_domain'"
                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 {{ $errors->has('primary_domain') ? 'border-red-500 ring-red-500 ring-1' : '' }}" />
                </div>
                @error('primary_domain')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Email Field -->
            <div class="space-y-2">
                <x-laravel.label for="email" value="{{ __('Email (Must match domain)') }}" class="text-sm font-medium text-gray-700" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <x-icons.lucide.mail class="h-5 w-5 text-gray-400" />
                    </div>
                    <x-laravel.input id="email"
                        name="email"
                        type="email"
                        :value="old('email')"
                        required
                        autocomplete="username"
                        placeholder="<EMAIL>"
                        :autofocus="$firstErrorField === 'email'"
                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 {{ $errors->has('email') ? 'border-red-500 ring-red-500 ring-1' : '' }}" />
                </div>
                @error('email')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>



            <!-- Country Field -->
            <div class="space-y-2">
                <x-laravel.label for="country_id" value="Country" class="text-sm font-medium text-gray-700" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                        <x-icons.lucide.map-pin class="h-5 w-5 text-gray-400" />
                    </div>
                    <select id="country_id"
                        name="country_id"
                        tabindex="0"
                        class="select2 block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 @error('country') border-red-500 ring-red-500 ring-1 @enderror"
                        required>
                        <option value="">Select your country</option>
                        @foreach ($countries as $country)
                            <option value="{{ $country->id }}" {{ old('country_id') == $country->id ? 'selected' : '' }}>
                                {{ $country->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                @error('country_id')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Password Field -->
            <div class="space-y-2">
                <x-laravel.label for="password" value="{{ __('Password') }}" class="text-sm font-medium text-gray-700" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <x-icons.lucide.lock class="h-5 w-5 text-gray-400" />
                    </div>
                    <x-laravel.input id="password"
                        name="password"
                        type="password"
                        required
                        autocomplete="new-password"
                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 {{ $errors->has('password') ? 'border-red-500 ring-red-500 ring-1' : '' }}"
                        placeholder="Create a strong password" />
                </div>
                @error('password')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Confirm Password Field -->
            <div class="space-y-2">
                <x-laravel.label for="password_confirmation" value="{{ __('Confirm Password') }}" class="text-sm font-medium text-gray-700" />
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <x-icons.lucide.check class="h-5 w-5 text-gray-400" />
                    </div>
                    <x-laravel.input id="password_confirmation"
                        name="password_confirmation"
                        type="password"
                        required
                        autocomplete="new-password"
                        class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200 {{ $errors->has('password_confirmation') ? 'border-red-500 ring-red-500 ring-1' : '' }}"
                        placeholder="Confirm your password" />
                </div>
                @error('password_confirmation')
                    <p class="text-sm text-red-600 mt-1">{{ $message }}</p>
                @enderror
            </div>

            <!-- Terms and Privacy -->
            @if (Laravel\Jetstream\Jetstream::hasTermsAndPrivacyPolicyFeature())
            <div class="space-y-2">
                <x-laravel.label for="terms">
                    <div class="flex items-start">
                        <x-laravel.checkbox name="terms" id="terms" required class="mt-1 rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500" />
                        <div class="ml-3">
                            <p class="text-sm text-gray-600">
                                {!! __('I agree to the :terms_of_service and :privacy_policy', [
                                'terms_of_service' => '<a target="_blank" href="'.route('terms.show').'"
                                    class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">'.__('Terms of Service').'</a>',
                                'privacy_policy' => '<a target="_blank" href="'.route('policy.show').'"
                                    class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">'.__('Privacy Policy').'</a>',
                                ]) !!}
                            </p>
                        </div>
                    </div>
                </x-laravel.label>
            </div>
            @endif

            <!-- Submit Button -->
            <div class="space-y-4">
                <x-laravel.button class="w-full justify-center py-3 text-base font-medium">
                    {{ __('Create Publisher Account') }}
                </x-laravel.button>
            </div>
        </form>

        <!-- Account Type Options -->
        <div class="mt-6 pt-4 border-t border-gray-200">
            <div class="text-center mb-3">
                <p class="text-sm text-gray-600">Or choose a different account type</p>
            </div>

            <!-- Advertiser Registration -->
            <div class="relative group">
                <a href="{{ route('register') }}"
                   class="block p-3 border-2 border-gray-200 bg-gray-50 rounded-lg hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200 group-hover:shadow-md">
                    <div class="flex items-center justify-center mb-1">
                        <x-icons.lucide.user class="w-4 h-4 text-gray-600 group-hover:text-indigo-600 mr-2 transition-colors duration-200" />
                        <span class="text-sm font-semibold text-gray-700 group-hover:text-indigo-700 transition-colors duration-200">Register as Advertiser</span>
                    </div>
                    <p class="text-xs text-gray-600 text-center">Create campaigns and reach your target audience</p>
                </a>
            </div>
        </div>

        <!-- Sign In Link -->
        <div class="mt-4 text-center">
            <p class="text-xs text-gray-600">
                Already have an account?
                <a class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200 ml-1"
                   href="{{ route('login') }}">
                    {{ __('Sign in') }}
                </a>
            </p>
        </div>
    </x-laravel.authentication-card>
    @push('scripts')
        <script>
            $(function() {
                const $select = $('#country_id');

                $select.select2({
                    placeholder: 'Select your country',
                    width: '100%',
                    selectionCssClass: 'tailwind-select',
                    dropdownCssClass: 'tailwind-dropdown',
                    minimumResultsForSearch: 5,
                });

                // Focus search input when dropdown opens
                $select.on('select2:open', function() {
                    let searchInput = document.querySelector('.select2-container--open .select2-search__field');
                    if (searchInput) {
                        searchInput.focus();
                    }
                });
                // Fix tabbing behavior
                $select.on('select2:select', function() {
                    // Move focus to the next element after selection
                    const formElements = $('form').find(':input:visible');
                    const index = formElements.index($select);
                    if (index > -1 && index + 1 < formElements.length) {
                        formElements.eq(index + 1).focus();
                    }
                });

                // Custom handling for tab into Select2 (email -> country)
                $('#email').on('keydown', function(e) {
                    if (e.key === 'Tab' && !e.shiftKey) { // Tab forward from email
                        e.preventDefault();
                        $select.select2('open');
                    }
                });

                // Optional: handle shift+tab from Select2 back to email
                $('.select2-selection').on('keydown', function(e) {
                    if (e.key === 'Tab' && e.shiftKey) {
                        e.preventDefault();
                        $('#email').focus();
                    }
                });
            });
        </script>
    @endpush

</x-guest-layout>
