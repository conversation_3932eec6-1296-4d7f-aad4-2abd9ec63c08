<div id="app-body" class="app-body flex">
    <div id="main-section" class="flex flex-col w-full overflow-hidden">
        <div id="body-wrapper" class="my-0 lg:my-4 m-0 lg:m-10 pb-6">
            {{-- Order Item Information Box --}}
            <div class="flex flex-col md:flex-row gap-0 sm:gap-6 mt-6">
                <div id="order-details" class="box-section-wrapper flex-1">
                    {{-- Intro --}}
                    <div id="intro-section" class="mb-10">
                        <div id="page-title" class="text-2xl font-bold text-slate-800 tracking-tight">
                            Order Item Information
                        </div>
                        <div id="page-info" class="text-sm text-slate-600 pt-1.5">
                            Detailed information about this specific order item.
                        </div>
                    </div>

                    {{-- Data Info --}}
                    <ul class="order-meta space-y-3 columns-1 md:columns-2 lg:columns-3 text-sm">
                        <li>
                            <span class="o-label font-bold text-slate-600">Order Item ID:</span>
                            <span class="o-data text-teal-700 font-medium">#{{ $orderItem->id }}</span>
                        </li>

                        <li>
                            <span class="o-label font-bold text-slate-600">Date Created:</span>
                            <span class="o-data text-teal-700 font-medium">{{ date_format($orderItem->created_at, "d M
                                Y") }}</span>
                        </li>

                        <li class="flex items-center">
                            <span class="o-label font-bold text-slate-600 pr-1">State:</span>
                            <x-ui.badges.order.statusBadge :item="$orderItem" />
                        </li>

                        <li class="flex flex-row items-center gap-1">
                            <span class="o-label font-bold text-slate-600">Website:</span>
                            <a href="https://{{ $orderItem->website->website_domain }}" target="_blank"
                                class="o-data text-teal-700 font-medium">
                                <div class="flex items-center">
                                    <img width="16" height="16"
                                        src="https://www.google.com/s2/favicons?sz=64&domain_url={{ $orderItem->website->website_domain }}"
                                        class="w-4 h-4 border rounded-full mr-1.5" alt="Website favicon">
                                    {{ $orderItem->website->website_domain }}
                                </div>
                            </a>
                        </li>

                        <li>
                            <span class="o-label font-bold text-slate-600">Publisher:</span>
                            <span class="o-data text-teal-700 font-medium">{{ $orderItem->website->publisher?->name
                                }}</span>
                        </li>

                        <li>
                            <span class="o-label font-bold text-slate-600">Published URL:</span>
                            <span class="o-data text-teal-700 font-medium">
                                @if($orderItem->publication?->publication_url)
                                <a href="https://{{ $orderItem->publication->publication_url }}"
                                    class="flex items-center">
                                    <span class="mr-1">{{ $orderItem->publication->publication_url }}</span>
                                    <span class="w-4 text-green-600">
                                        <x-icons.lucide.external-link />
                                    </span>
                                </a>
                                @else
                                <span class="text-xs text-gray-300">Awaiting Publication</span>
                                @endif
                            </span>
                        </li>

                        <li>
                            <span class="o-label font-bold text-slate-600">Last Updated:</span>
                            <span class="o-data text-teal-700 font-medium">{{ date_format($orderItem->updated_at, "d M
                                Y") }}</span>
                        </li>

                        <li class="flex items-center">
                            <span class="o-label font-bold text-slate-600 pr-1">Niche:</span>
                            <span class="o-data text-teal-700 font-medium">{{ ucfirst($orderItem->niche) }}</span>
                        </li>

                        <li class="flex items-center">
                            <span class="o-label font-bold text-slate-600 pr-1">Price:</span>
                            <span class="o-data text-teal-700 font-medium">${{ $orderItem->price_paid }}</span>
                        </li>
                    </ul>
                </div>

                {{-- State Information Box --}}
                <div class="box-section-wrapper w-full md:w-80">
                    <div class="flex flex-col items-start relative">
                        {{-- Title --}}
                        <div class="pb-0 w-full">
                            <h3 class="text-base font-semibold text-gray-900 w-full">Status</h3>
                        </div>

                        {{-- Timeline Container --}}
                        <div class="flex flex-col items-start space-y-6 relative mt-6">
                            {{-- Vertical line --}}
                            <div
                                class="absolute w-[1px] left-4 border-l-2 border-l-gray-300 border-dashed h-full transform -translate-x-1/2">
                            </div>

                            {{-- Timeline Items --}}
                            @foreach($timeline as $item)
                            <div class="flex items-start space-x-4 relative">
                                {{-- Timeline Circle & Icon --}}
                                <div class="relative flex items-center justify-center z-10">
                                    <div
                                        class="w-8 h-8 flex items-center justify-center rounded-full border-2 {{ $item['completed'] ? 'border-green-500 bg-green-100' : 'border-gray-400 bg-white' }}">
                                        @if($item['completed'] && $loop->last)
                                        <x-icons.lucide.check class="text-green-500 w-4 h-4" />
                                        @elseif($item['completed'])
                                        <x-icons.lucide.check class="text-green-500 w-4 h-4" />
                                        @else
                                        <x-icons.lucide.hourglass
                                            class="{{ ($item['state'] === $orderItem->state_name) ? 'text-green-500' : 'text-gray-400' }} w-4 h-4" />
                                        @endif
                                    </div>
                                </div>

                                {{-- Timeline Content --}}
                                <div class="flex-1">
                                    <span
                                        class="text-base font-semibold {{ ($item['state'] === $orderItem->state_name) ? 'text-green-600' : 'text-gray-700' }}">
                                        {{ $item['label'] }}
                                    </span>

                                    @php
                                    $stateChangeLog = $orderItem->stateChangeLogs()->where('from_state',
                                    $item['state'])->first();
                                    @endphp

                                    @if($stateChangeLog)
                                    <div class="flex items-center mt-1 text-xs text-gray-600">
                                        <x-icons.lucide.clock class="w-4 h-4 text-gray-600 mr-1" />
                                        <span class="text-gray-600">
                                            {{ $stateChangeLog->created_at->format('d M Y H:i') }}
                                        </span>
                                    </div>
                                    @elseif($item['completed'] && $loop->last)
                                    <div class="flex items-center mt-1 text-xs text-gray-600">
                                        <x-icons.lucide.clock class="w-4 h-4 text-gray-600 mr-1" />
                                        <span class="text-gray-600">
                                            {{ $orderItem->updated_at->format('d M Y H:i') }}
                                        </span>
                                    </div>
                                    @endif
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

            {{-- Additional Sections --}}
            @if($orderItem->state instanceof \App\States\OrderItem\RequirementsPending)
            <x-marketplace.order.pending-requirements-message />
            @endif

            {{-- Approved Requirements Section --}}
            @if($orderItem->requirements && $orderItem->state_name !=
            \App\Enums\OrderItemStates::RequirementsPending->value && $orderItem->state_name !=
            \App\Enums\OrderItemStates::RequirementAwaitingPublisherApproval->value)
            <div class="box-section-wrapper mt-6">
                <div class="mb-6">
                    <div class="flex items-center">
                        <div class="w-8 h-8 rounded-full bg-teal-100 flex items-center justify-center mr-3">
                            <x-icons.lucide.check class="w-5 h-5 text-teal-600" />
                        </div>
                        <div>
                            <div class="text-xl font-bold text-slate-800 tracking-tight">
                                Approved Requirements
                            </div>
                            <div class="text-sm text-slate-600 pt-1">
                                The requirements that have been approved for this order item.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-slate-50 rounded-lg p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="flex items-start">
                            <div class="w-6 h-6 rounded-full bg-teal-100 flex items-center justify-center mr-3 mt-0.5">
                                <x-icons.lucide.globe-earth class="w-4 h-4 text-teal-600" />
                            </div>
                            <div>
                                <div class="font-bold text-slate-600 mb-1">Article Topic</div>
                                <div class="text-teal-700 font-medium">{{ $orderItem->requirements->article_topic }}
                                </div>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-6 h-6 rounded-full bg-teal-100 flex items-center justify-center mr-3 mt-0.5">
                                <x-icons.lucide.link class="w-4 h-4 text-teal-600" />
                            </div>
                            <div>
                                <div class="font-bold text-slate-600 mb-1">Anchor Text</div>
                                <div class="text-teal-700 font-medium">{{ $orderItem->requirements->anchor_text }}</div>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-6 h-6 rounded-full bg-teal-100 flex items-center justify-center mr-3 mt-0.5">
                                <x-icons.lucide.globe-earth class="w-4 h-4 text-teal-600" />
                            </div>
                            <div>
                                <div class="font-bold text-slate-600 mb-1">Advertiser URL</div>
                                <a href="{{ $orderItem->requirements->advertiser_url }}" target="_blank"
                                    class="text-teal-700 font-medium flex items-center hover:text-teal-800 transition-colors">
                                    <span class="mr-1">{{ $orderItem->requirements->advertiser_url }}</span>
                                    <span class="w-4 text-green-600">
                                        <x-icons.lucide.external-link />
                                    </span>
                                </a>
                            </div>
                        </div>

                        @if($orderItem->requirements->requirement_comments)
                        <div class="flex items-start">
                            <div class="w-6 h-6 rounded-full bg-teal-100 flex items-center justify-center mr-3 mt-0.5">
                                <x-icons.lucide.message-square class="w-4 h-4 text-teal-600" />
                            </div>
                            <div>
                                <div class="font-bold text-slate-600 mb-1">Additional Comments</div>
                                <div class="text-teal-700 font-medium">
                                    {{ $orderItem->requirements->requirement_comments }}
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endif

            @if($orderItem->state_name === \App\Enums\OrderItemStates::OrderItemCancelled->value)
            <div class="box-section-wrapper mt-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold text-slate-800 tracking-tight">Request Refund</h3>
                        <p class="text-sm text-slate-600 mt-1">You can request a refund for this canceled order item.
                        </p>
                    </div>
                    <button wire:click="requestRefund"
                            wire:loading.attr="disabled"
                            wire:target="requestRefund"
                            class="inline-flex items-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-700 focus:bg-red-700 active:bg-red-900 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition ease-in-out duration-150 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span wire:loading.remove wire:target="requestRefund">
                            Request Refund
                        </span>
                        <span wire:loading wire:target="requestRefund" class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Processing...
                        </span>
                    </button>
                </div>
            </div>
            @elseif($orderItem->state_name === \App\Enums\OrderItemStates::OrderItemRefundRequested->value)
            <div class="box-section-wrapper mt-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold text-slate-800 tracking-tight">Refund Request</h3>
                        <p class="text-sm text-slate-600 mt-1">Your refund request has been submitted.</p>
                    </div>
                </div>
            </div>
            @elseif($orderItem->state_name === \App\Enums\OrderItemStates::RefundedToWallet->value)
            <div class="box-section-wrapper mt-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-xl font-bold text-slate-800 tracking-tight">Refunded</h3>
                        <p class="text-sm text-slate-600 mt-1">
                            Your refund has been credited to your wallet.
                        </p>
                    </div>
                    <a href="{{ route('advertiser.wallet.index') }}"
                        class="inline-flex items-center px-4 py-2 bg-teal-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-teal-700 focus:bg-teal-700 active:bg-teal-900 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 transition ease-in-out duration-150 transform hover:scale-105 group relative overflow-hidden ml-6">
                        View Wallet
                        <span class="ml-2 transition-transform duration-300 transform group-hover:translate-x-1">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 5l7 7-7 7" />
                            </svg>
                        </span>
                    </a>
                </div>
            </div>
            @endif

            <livewire:marketplace.order.order-item-requirements-box-detail :item="$orderItem" />

        </div>
    </div>
</div>