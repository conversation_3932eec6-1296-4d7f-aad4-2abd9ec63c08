<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice</title>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
        rel="stylesheet">

    <style>
        body {
            font-family: Inter, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            line-height: 140%;
        }

        .invoice-container {
            max-width: 900px;
            margin: auto;
            background-color: #fff;
            padding: 20px 25px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .header,
        .billing,
        .items,
        .total,
        .terms {
            margin-bottom: 20px;
        }

        .header h2,
        .billing h2,
        .items h2 {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .header div,
        .billing div,
        .items div {
            margin-bottom: 10px;
        }

        .invoice-number {
            text-align: right;
        }

        .amount {
            font-size: 26px;
            font-weight: 600;
            color: #333;
            text-align: right;
        }

        .paid-status {
            display: inline-block;
            background-color: #4CAF50;
            color: #fff;
            font-weight: bold;
            padding: 5px 10px;
            font-size: 16px;
            border-radius: 5px;
            margin-top: 10px;
            text-transform: capitalize;
        }

        .billing,
        .items {
            border-top: 1px solid #ddd;
            padding-top: 30px;
            margin-top: 10px;
        }

        .billing h2,
        .items h2 {
            margin-top: 0;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .items-table th,
        .items-table td {
            text-align: left;
            padding: 8px;
            border-bottom: 1px solid #ddd;
        }

        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .total {
            text-align: right;
        }

        .terms p {
            font-size: 13px;
            color: #888;
        }

        .font-one {
            font-size: 15px;
        }

        .note-title {
            font-weight: bold;
            font-size: 16px;
            margin-top: 40px;
            border-top: 1px solid #ddd;
            padding-top: 30px;
            margin-top: 10px;
        }

        @media print {
            .hide-on-print {
                display: none;
            }
        }
    </style>
</head>

<body>

    <div class="invoice-container">

        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <div style="display: flex; align-items: center;">
                <x-icons.etc.bear />
                <h2 style="font-size:22px;">Pressbear</h2>
            </div>

            {{-- Download Option --}}
            <div class="hide-on-print">
                <button onclick="downloadAsPDF()" style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        border: none;
                        padding: 12px 24px;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 600;
                        cursor: pointer;
                        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
                        transition: all 0.3s ease;
                        display: inline-flex;
                        align-items: center;
                        gap: 8px;
                    "
                    onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.4)'"
                    onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.3)'">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="7,10 12,15 17,10"></polyline>
                        <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    Download PDF
                </button>
            </div>
        </div>

        <div class="header" style="display: flex; justify-content: space-between;">
            <div class="font-one">




                <div>Softsynk LLC</div>
                <div id="our-address" class="">
                    8 Octavia St
                    <br>
                    San Francisco, CA 94102
                    <br>
                    United States
                </div>
                <div><EMAIL></div>
            </div>

            <div class="invoice-number font-one" style="margin-top: 20px;">
                {{-- month+year+dash+order-id --}}
                <div>
                    Invoice #{{ $order->created_at->format('my') }}-{{ $order->id }}
                    <br>
                    Date: {{ $order->created_at->format('M d, Y') }}
                </div>
                @php
                $statusClass = match($order->status) {
                'delivered' => 'bg-green-100 text-green-800',
                'inprogress' => 'bg-yellow-100 text-yellow-800',
                'pending' => 'bg-yellow-100 text-gray-800',
                'cancelled' => 'bg-red-100 text-red-800',
                default => 'bg-gray-100 text-gray-800',
                };
                @endphp

                <span class="px-2 py-1 rounded-full text-xs font-semibold {{ $statusClass }}">
                    {{ ucfirst($order->status) }}
                </span>

            </div>
        </div>



        <div class="amount">
            <div>${{ $order->price_paid }}</div>

            @if($order->payment->status == 'paid')
            <div class="paid-status">{{ $order->payment->status }}</div>
            @elseif($order->payment->status == 'refunded')
            <div class="paid-status" style="background-color: #f97316;">Refunded</div>
            @elseif($order->payment->status == 'partially_refunded')
            <div class="paid-status" style="background-color: #854d0e;">Partially Refunded</div>
            @else
            <div class="paid-status">{{ $order->payment->status ?? 'Paid' }}</div>
            @endif

        </div>

        <div class="billing font-one">
            <h2>Bill to</h2>
            <div class="">{{ $user->company ?? $user->name }}</div>
            <div id="customer-address">
                {{ $user->address_data['address'] ?? '' }}<br>
                {{ $user->address_data['city'] ?? '' }}<br>
                {{ $user->address_data['postal_code'] ?? '' }}<br>
                {{ $user->country->name ?? '' }}
            </div>
            <div>{{ $user->email ?? '' }}</div>
        </div>

        <div class="items">
            <h2 style="padding-bottom: 4px;">Order Items</h2>
            <table class="items-table">
                <tr>
                    <th>Media Publications</th>
                    <th>Amount</th>
                </tr>

                @foreach($order->orderItems as $item)
                <tr>
                    <td>
                        <span style="margin-right: 6px; color: #666;">{{ $loop->iteration }}.</span>
                        <span>{{ $item->website->website_domain }}</span>
                        <br>
                    <td>${{ $item->price_paid }}</td>
                </tr>
                @endforeach
            </table>
        </div>

        <div class="total">
            <div>Subtotal: ${{ $order->price_paid }}</div>
            <div style="margin-top: 10px;"><strong>Total: ${{ $order->price_paid }}</strong></div>
        </div>


        <div class="note">
            <div class="note-title">Advertiser Note</div>
            <div>{{ $order_memo }}</div>
        </div>


        <div class="terms" style="margin-top: 40px;">
            <p>Terms and condtions: https://pressbear.com/terms-of-service</p>
        </div>

    </div>




    <script>
        function downloadAsPDF() {
            window.print();
        }
    </script>

</body>

</html>