<template>
    <div class="space-y-6">
        <!-- Header Section -->
        <div class="border-b pb-4">
            <h3 class="text-lg font-medium text-gray-900">Content Details</h3>
            <p class="mt-1 text-sm text-gray-500">Review the content provided by the customer.</p>
        </div>

        <div v-if="content" class="space-y-4">
            <!-- Title and URL Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <!-- Title Section -->
                <div class="bg-white border border-gray-200 rounded-lg p-3">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Title</h4>
                    <p class="text-sm text-gray-700">{{ content?.title || 'No title provided' }}</p>
                </div>

                <!-- Content URL Section -->
                <div v-if="content?.content_url" class="bg-white border border-gray-200 rounded-lg p-3">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Content URL</h4>
                    <a :href="content.content_url" target="_blank"
                       class="inline-flex items-center text-sm text-indigo-600 hover:text-indigo-800 hover:underline break-all">
                        <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                        {{ content.content_url }}
                    </a>
                </div>

                <!-- Empty div to maintain grid when no URL -->
                <div v-else></div>
            </div>

            <!-- Content Body Section with Improved Layout -->
            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
                    <h4 class="text-sm font-medium text-gray-900">Content</h4>
                    <button
                        @click="toggleExpanded"
                        class="text-xs text-gray-500 hover:text-gray-700 flex items-center gap-1"
                        v-if="content?.content_body && content.content_body.length > 500"
                    >
                        <span>{{ isExpanded ? 'Collapse' : 'Expand' }}</span>
                        <svg class="w-3 h-3 transition-transform" :class="{ 'rotate-180': isExpanded }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                </div>
                <div class="relative">
                    <div
                        class="p-4 prose prose-sm max-w-none overflow-hidden transition-all duration-300"
                        :class="{
                            'max-h-40': !isExpanded && content?.content_body && content.content_body.length > 500,
                            'max-h-none': isExpanded || !content?.content_body || content.content_body.length <= 500
                        }"
                    >
                        <div v-html="content?.content_body || 'No content provided'" class="leading-relaxed"></div>
                    </div>
                    <!-- Fade overlay for collapsed content -->
                    <div
                        v-if="!isExpanded && content?.content_body && content.content_body.length > 500"
                        class="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent pointer-events-none"
                    ></div>
                </div>
            </div>

            <!-- Attached Files Section -->
            <div v-if="media?.length" class="bg-white border border-gray-200 rounded-lg overflow-hidden">
                <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
                    <h4 class="text-sm font-medium text-gray-900">Attached Files ({{ media.length }})</h4>
                </div>
                <div class="p-4">
                    <div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
                        <div v-for="file in media" :key="file.id"
                             class="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors">
                            <div class="flex items-center space-x-3 min-w-0 flex-1">
                                <div class="flex-shrink-0">
                                    <!-- Image Preview for image files -->
                                    <div v-if="isImageFile(file)" class="relative group">
                                        <img :src="file.url"
                                             :alt="file.name"
                                             class="w-16 h-16 object-cover rounded-lg shadow-sm border border-gray-200 cursor-pointer hover:shadow-md transition-shadow"
                                             @click="openImageModal(file)"
                                             @error="handleImageError">
                                        <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200 flex items-center justify-center">
                                            <svg class="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <!-- Generic file icon for non-image files -->
                                    <div v-else class="w-16 h-16 flex items-center justify-center bg-gray-100 rounded-lg border border-gray-200">
                                        <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ file.name }}</p>
                                    <div class="flex items-center space-x-2 text-xs text-gray-500">
                                        <span>{{ formatFileSize(file.size) }}</span>
                                        <span v-if="isImageFile(file)" class="inline-flex items-center px-2 py-0.5 rounded-full bg-green-100 text-green-800 font-medium">
                                            Image
                                        </span>
                                        <span v-else class="inline-flex items-center px-2 py-0.5 rounded-full bg-blue-100 text-blue-800 font-medium">
                                            {{ getFileType(file) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <a :href="file.url" target="_blank"
                               class="flex-shrink-0 inline-flex items-center px-3 py-1.5 text-xs font-medium text-indigo-700 bg-indigo-100 hover:bg-indigo-200 rounded-md transition-colors">
                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                {{ isImageFile(file) ? 'View Full Size' : 'Download' }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- No Files Message -->
            <div v-else class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h4 class="text-sm font-medium text-gray-900 mb-2">No Files Attached</h4>
                <p class="text-sm text-gray-500">No media files have been uploaded for this content.</p>
            </div>

            <!-- Comments and Metadata Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <!-- Comments Section -->
                <div v-if="content?.comments" class="lg:col-span-2 bg-white border border-gray-200 rounded-lg p-3">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Comments</h4>
                    <p class="text-sm text-gray-700">{{ content.comments }}</p>
                </div>

                <!-- Compact Metadata -->
                <div class="bg-white border border-gray-200 rounded-lg p-3" :class="content?.comments ? '' : 'lg:col-span-3'">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Details</h4>
                    <div class="space-y-2 text-xs">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-500">Source:</span>
                            <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                                  :class="content?.content_source === 'customer' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'">
                                {{ content?.content_source === 'customer' ? 'Customer' : 'Team' }}
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-500">Writer:</span>
                            <span class="text-gray-900 font-medium">{{ content?.writer?.name || 'Not assigned' }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-500">Created:</span>
                            <span class="text-gray-900">{{ content?.created_at_formatted || 'N/A' }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-500">Updated:</span>
                            <span class="text-gray-900">{{ content?.updated_at_formatted || 'N/A' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div v-else class="text-center py-4">
            <p class="text-gray-500">No content available</p>
        </div>

        <!-- Image Modal -->
        <div v-if="selectedImage"
             class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
             @click="closeImageModal">
            <div class="relative max-w-4xl max-h-full">
                <button @click="closeImageModal"
                        class="absolute -top-10 right-0 text-white hover:text-gray-300 transition-colors">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <img :src="selectedImage.url"
                     :alt="selectedImage.name"
                     class="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
                     @click.stop>
                <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white p-4 rounded-b-lg">
                    <p class="text-sm font-medium">{{ selectedImage.name }}</p>
                    <p class="text-xs text-gray-300">{{ formatFileSize(selectedImage.size) }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

defineProps({
    media: {
        type: Array,
        required: true,
        default: () => ([])
    },
    content: {
        type: Object,
        required: true,
        default: () => ({})
    }
})

// Reactive state for expand/collapse functionality
const isExpanded = ref(false)
const selectedImage = ref(null)

const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value
}

// Image modal functions
const openImageModal = (file) => {
    selectedImage.value = file
}

const closeImageModal = () => {
    selectedImage.value = null
}

// File type checking functions
const isImageFile = (file) => {
    return file.mime_type && file.mime_type.startsWith('image/')
}

const getFileType = (file) => {
    if (!file.mime_type) return 'File'

    const mimeType = file.mime_type.toLowerCase()
    if (mimeType.includes('pdf')) return 'PDF'
    if (mimeType.includes('word') || mimeType.includes('document')) return 'Document'
    if (mimeType.includes('text')) return 'Text'
    if (mimeType.includes('image')) return 'Image'

    return 'File'
}

const handleImageError = (event) => {
    console.error('Failed to load image:', event.target.src)
    // Optionally show a placeholder or error message
}

const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
</script>
