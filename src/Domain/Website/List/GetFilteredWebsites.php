<?php

namespace Domain\Website\List;

use App\Models\MarketplaceWebsite;
use Illuminate\Pagination\LengthAwarePaginator;

class GetFilteredWebsites
{
    /*********************************************************************
     * GET FILTERED WEBSITES
     *********************************************************************
     *
     * Get filtered websites based on the filters provided.
     *
     * @return Collection
     ********************************************************************/

    public function getFiltered(array $filters): LengthAwarePaginator
    {
        $query = MarketplaceWebsite::withCount('orders');

        $searchTerm = $filters['searchTerm'] ?? null;
        $status = $filters['status'] ?? null;
        $sortField = $filters['sortField'] ?? 'id';
        $sortOrder = $filters['sortOrder'] ?? 'desc';
        $perPage = $filters['perPage'] ?? 10;

        $allowedSortFields = [
            'id',
            'website_domain',
            'orders_count',
            'guest_post_price',
            'updated_at',
            'active',
        ];

        if (! in_array($sortField, $allowedSortFields, true)) {
            $sortField = 'id';
        }

        if ($searchTerm) {
            $query->where('website_domain', 'like', "%$searchTerm%");
        }

        if (in_array($status, ['0', '1'], true)) {
            $query->where('active', $status);
        }

        $query->orderBy($sortField, $sortOrder);

        return $query->paginate($perPage)->withQueryString();
    }
}
