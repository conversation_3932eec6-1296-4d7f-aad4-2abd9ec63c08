<?php

namespace App\Livewire\Marketplace\Order;

use App\Models\MarketplaceSingleOrderItem;
use App\Notifications\States\RequirementReminderPublisher;
use App\States\OrderItem\ContentAwaitingPublisherApproval;
use App\States\OrderItem\ContentRevisionRequestedByAdvertiser;
use App\States\OrderItem\OrderItemCompleted;
use App\States\OrderItem\PublicationRevisionRequestedByAdvertiser;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;
use Livewire\Component;

/*********************************************************************
 * ORDER ITEM REQUIREMENTS BOX DETAIL COMPONENT
 *********************************************************************
 *
 * Manages the requirements box detail view for marketplace order items.
 * Handles content approval, revision requests, and communication features.
 *
 * Features:
 * - Content approval workflow
 * - Revision request management
 * - Email reminder system
 * - Chat functionality
 *
 * @param MarketplaceSingleOrderItem $item
 * The order item being managed
 *
 *********************************************************************/
class OrderItemRequirementsBoxDetail extends Component
{
    public MarketplaceSingleOrderItem $item;
    public $showResendButton = false;
    public $showChat = false;
    public $showDisapproveModal = false;
    public $showRevisionModal = false;
    public $revisionReason = '';
    public $revisionType = '';
    public $revisionPriority = 'medium';


    /*********************************************************************
     * INITIALIZE COMPONENT
     *********************************************************************
     *
     * Sets up initial component state and checks for chat parameter.
     * Initializes resend button visibility based on notification history.
     *
     * @return void
     *
     *********************************************************************/
    public function mount(): void
    {
        $this->checkResendButtonVisibility();
        if (isset($_GET['openChat'])) {
            $this->showChat = true;
        }
    }


    /*********************************************************************
     * CHECK RESEND BUTTON VISIBILITY
     *********************************************************************
     *
     * Determines if the resend button should be visible based on:
     * - Last notification sent time
     * - Item update time
     * - 24-hour cooldown period
     *
     * @return void
     *
     *********************************************************************/
    private function checkResendButtonVisibility(): void
    {
        // -----------------------
        // Initialize Time Window
        $last24Hours = now()->subHours(24);
        $publisher = $this->item->website->publisher;

        if ($publisher) {
            // -----------------------
            // Get Latest Notification
            $latestNotification = $publisher->notifications()
                ->where('type', RequirementReminderPublisher::class)
                ->orderByDesc('created_at')
                ->first();

            // -----------------------
            // Set Button Visibility
            if (! $latestNotification) {
                $this->showResendButton = $this->item->updated_at->lte($last24Hours);
            } else {
                $this->showResendButton = Carbon::parse($latestNotification->created_at)->lte($last24Hours);
            }
        }
    }


    /*********************************************************************
     * RESEND REQUIREMENT EMAIL
     *********************************************************************
     *
     * Sends a reminder email to the publisher about pending requirements.
     * Only works when item is in requirement-awaiting-publisher-approval state.
     *
     * @return void
     *
     *********************************************************************/
    public function resendEmail(): void
    {
        if ($this->item->state_name === 'requirement-awaiting-publisher-approval') {
            Notification::send($this->item->website->publisher, new RequirementReminderPublisher($this->item));
            $this->js("toast('Reminder email sent successfully', {type: 'success', position: 'bottom-center'})");
            $this->checkResendButtonVisibility();
        }
    }


    /*********************************************************************
     * TOGGLE CHAT VISIBILITY
     *********************************************************************
     *
     * Controls the visibility of the chat interface.
     *
     * @return void
     *
     *********************************************************************/
    public function openChat(): void
    {
        $this->showChat = true;
    }

    public function closeChat(): void
    {
        $this->showChat = false;
    }


    /*********************************************************************
     * APPROVE CONTENT
     *********************************************************************
     *
     * Transitions the order item to content awaiting publisher approval state.
     * Handles success and error cases with appropriate notifications.
     *
     * @return void
     *
     *********************************************************************/
    public function approveContent(): void
    {
        try {
            $this->item->state->transitionTo(ContentAwaitingPublisherApproval::class);
            $this->js("toast('Content approved successfully', {type: 'success', position: 'bottom-center'})");
        } catch (\Exception $e) {
            $this->js("toast('Failed to approve content: " . $e->getMessage() . "', {type: 'error', position: 'bottom-center'})");
        }
    }


    /*********************************************************************
     * APPROVE PUBLICATION
     *********************************************************************
     *
     * Marks the order item as completed after publication approval.
     * Handles success and error cases with appropriate notifications.
     *
     * @return void
     *
     *********************************************************************/
    public function approvePublication(): void
    {
        try {
            $this->item->state->transitionTo(OrderItemCompleted::class);
            $this->item->save();
            $this->js("toast('Publication approved successfully', {type: 'success', position: 'bottom-center'})");
        } catch (\Exception $e) {
            $this->js("toast('Failed to approve publication: " . $e->getMessage() . "', {type: 'error', position: 'bottom-center'})");
        }
    }


    /*********************************************************************
     * DISAPPROVE CONTENT
     *********************************************************************
     *
     * Handles content disapproval with revision request.
     * Updates content with revision reason and transitions state.
     *
     * @return void
     *
     *********************************************************************/
    public function disapproveContent(): void
    {
        try {
            // -----------------------
            // Validate Revision Reason
            if (empty($this->revisionReason)) {
                $this->js("toast('Please provide a reason for the revision request', {type: 'error', position: 'bottom-center'})");
                return;
            }

            // -----------------------
            // Update Content and State
            $this->item->content->update([
                'advertiser_revision_reason' => $this->revisionReason,
            ]);

            $this->item->state->transitionTo(ContentRevisionRequestedByAdvertiser::class);

            // -----------------------
            // Reset Form State
            $this->showDisapproveModal = false;
            $this->revisionReason = '';

            $this->js("toast('Revision request submitted successfully', {type: 'success', position: 'bottom-center'})");
        } catch (\Exception $e) {
            $this->js("toast('Failed to submit revision request: " . $e->getMessage() . "', {type: 'error', position: 'bottom-center'})");
        }
    }


    /*********************************************************************
     * SUBMIT REVISION REQUEST
     *********************************************************************
     *
     * Creates a new revision request for the order item.
     * Handles validation and state transition.
     *
     * @return void
     *
     *********************************************************************/
    public function submitRevisionRequest(): void
    {
        try {
            // -----------------------
            // Validate Input
            if (empty($this->revisionType) || empty($this->revisionReason)) {
                $this->js("toast('Please provide both revision type and details', {type: 'error', position: 'bottom-center'})");
                return;
            }

            // -----------------------
            // Create Revision Request
            $this->item->update([
                'advertiser_revision_reason' => $this->revisionReason,
            ]);

            // -----------------------
            // Update State and Reset Form
            $this->item->state->transitionTo(PublicationRevisionRequestedByAdvertiser::class);
            $this->showRevisionModal = false;
            $this->reset(['revisionType', 'revisionReason']);

            $this->js("toast('Revision request submitted successfully', {type: 'success', position: 'bottom-center'})");
        } catch (\Exception $e) {
            $this->js("toast('Failed to submit revision request: " . $e->getMessage() . "', {type: 'error', position: 'bottom-center'})");
        }
    }





    /*********************************************************************
     * RENDER COMPONENT
     *********************************************************************
     *
     * Renders the order item requirements box detail view.
     *
     * @return \Illuminate\View\View
     *
     *********************************************************************/
    public function render()
    {
        return view('livewire.marketplace.order.order-item-requirements-box-detail');
    }
}
